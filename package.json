{"name": "tdgdb", "version": "1.0.0", "main": "Server.js", "scripts": {"start": "node Server.js", "dev": "nodemon Server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.749.0", "@aws-sdk/client-ses": "^3.830.0", "@mailchimp/mailchimp_marketing": "^3.0.80", "@mailchimp/mailchimp_transactional": "^1.0.59", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "busboy": "^1.6.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-session": "^1.18.1", "fs": "^0.0.1-security", "google-auth-library": "^10.1.0", "jsonwebtoken": "^9.0.2", "md5": "^2.3.0", "mongodb": "^6.11.0", "mongoose": "^8.8.4", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "node-cron": "^4.1.0", "nodemailer": "^6.10.0", "otp-generator": "^4.0.1", "sharp": "^0.34.1", "websocket": "^1.0.35", "ws": "^8.18.2"}, "devDependencies": {"nodemon": "^3.1.7"}}