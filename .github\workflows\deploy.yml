name: Deploy to <PERSON><PERSON><PERSON>

on:
  push:
    branches:
      - main # or your production branch

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Cache Docker layers
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push Docker image
        run: |
          docker buildx build \
            --push \
            --cache-from=type=local,src=/tmp/.buildx-cache \
            --cache-to=type=local,dest=/tmp/.buildx-cache-new \
            -t khaledshoear/tdg-db .

      - name: Update Docker layer cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      - name: SSH to Hetzner server and update container
        uses: appleboy/ssh-action@v0.1.6
        with:
          host: ${{ secrets.HETZNER_HOST }}
          username: ${{ secrets.HETZNER_USER }}
          key: ${{ secrets.HETZNER_PRIVATE_KEY }}
          script: |
            docker pull khaledshoear/tdg-db
            docker stop tdg-db || true
            docker rm tdg-db || true
            docker run -d -p 5001:5000 --env-file TDG-DB/.env --name tdg-db khaledshoear/tdg-db
