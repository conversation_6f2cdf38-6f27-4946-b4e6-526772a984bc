name: Deploy to <PERSON><PERSON><PERSON> (PM2)

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Deploy via SSH
        uses: appleboy/ssh-action@v0.1.6
        with:
          host: ${{ secrets.HETZNER_HOST }}
          username: ${{ secrets.HETZNER_USER }}
          key: ${{ secrets.HETZNER_PRIVATE_KEY }}
          script: |
            cd /root/TDG-DB
            git pull origin main
            npm install --production
            pm2 restart Server
